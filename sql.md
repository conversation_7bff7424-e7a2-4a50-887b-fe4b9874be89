CREATE TABLE `fzh_order` (
  `order_id` char(20) NOT NULL DEFAULT '' COMMENT '订单ID，DD前缀',
  `order_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单类型：1消费订单，3充值订单',
  `order_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态 1、待支付，5已支付，6使用中，7，超时，9已完成',
  `org_id` int(11) NOT NULL DEFAULT '0' COMMENT '商户ID',
  `machine_id` int(11) DEFAULT '0' COMMENT '设备ID',
  `product_id` int(11) DEFAULT '0' COMMENT '产品ID',
  `product_name` varchar(168) DEFAULT '' COMMENT '产品名称',
  `time_length` int(11) DEFAULT '0' COMMENT '时长',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '微信userid',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `service_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `order_score` int(11) NOT NULL DEFAULT '0' COMMENT '订单积分',
  `pay_id` smallint(6) NOT NULL DEFAULT '0' COMMENT '支付方式：1、微信，2、余额',
  `pay_status` tinyint(4) DEFAULT '1' COMMENT '支付状态：1、未支付，5、已支付',
  `is_invoice` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否需要发票 1:需要,0:不需要',
  `order_remark` varchar(255) DEFAULT NULL COMMENT '客户备注',
  `close_reason` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单关闭原因',
  `del` tinyint(2) DEFAULT '0' COMMENT '删除状态',
  `pay_time` int(11) NOT NULL DEFAULT '0' COMMENT '支付时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_o` (`org_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单表';