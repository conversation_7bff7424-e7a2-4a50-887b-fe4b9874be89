<?php

abstract class BaseController extends Resource implements X5_api
{
    public $db;

    const success = 200;    //成功
    const failure = 400;    //失败

    private $startTime;
    private $acceptTime;

    public function __construct() {
        $this->startTime = microtime(true);
        $this->acceptTime = date('Y-m-d H:i:s');
        parent::__construct();

        $this->db = $this->getDBConnection('fzhl_db');
    }

    abstract public function run();

    public function getBody($params)
    {
        // 验证（ip验证，接口授权验证，数据安全性校验）
        $res = x5()->auth($params);
        $body = json_decode($params['body'], true);
        if (!is_array($body) || count($body) == 0) {
            throw new Exception("消息体为空", 1012);
        }
        return $body;
    }

    public function render($code, $msg, $body = "", $format = 'json')
    {
        x5()->output($code, $msg, NULL, $body, $format);
        exit();
    }

    /**
     * 获取参数
     * <AUTHOR> <<EMAIL>>
     * @param  array     $rules 参数检查规则
     * @return array
     */
    public function getParams($rules = null)
    {
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        if ($rules) {
            $this->checkParams($body, $rules);
        }

        return $body;
    }

    /**
     * 检查参数
     * <AUTHOR> <<EMAIL>>
     * @param  array    $args  参数
     * @param  array    $rules
     * @return boolean
     */
    public function checkParams($args, $rules)
    {
        foreach ($rules as $key => $val) {
            $rule = is_string($key) ? $val : '@';
            switch ($val) {
                case '@':
                    if (!isset($args[$key]))
                        throw new Exception("参数 '$key' 未传", 404);
                    break;
                default:
                    break;
            }
        }

        return true;
    }

    /**
     * 自动加载 model 类，其他类型待扩展
     * <AUTHOR> <<EMAIL>>
     */
    public static function modelLoader($className)
    {
        try {
            x5()->registerModel($className);
        } catch (Exception $e) {
            if ($e->getCode() == 1009)
                $errorInfo = '加载 Model ' . $className . '失败，若非 Model 类请手动加载;';
            else
                $errorInfo = $e->getMessage();

            throw new Exception($errorInfo);
        }
    }

    private static function getUserHostAddress()
    {
        switch (true) {
            case ($ip = getenv("HTTP_X_FORWARDED_FOR")):
                break;
            case ($ip = getenv("HTTP_CLIENT_IP")):
                break;
            default:
                $ip = getenv("REMOTE_ADDR") ? getenv("REMOTE_ADDR") : '127.0.0.1';
        }
        if (strpos($ip, ', ') !== false) {
            $ips = explode(', ', $ip);
            $ip = trim($ips[0]);
        }
        return $ip;
    }

    public function __destruct()
    {
        $content = '';
        if(isset($_REQUEST['post'])){
            $content = x5()->getParams('post');
        }
        $ip = self::getUserHostAddress();
        $method = $_SERVER['REQUEST_URI'];
        $requestTime = date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']);
        $runTime = microtime(true) - $this->startTime;
        $backTime = date('Y-m-d H:i:s');
        $back = ob_get_contents();
        $path = "/tmp/";
        $logName = "SpaceApi";

        CommonLog::log($path, $logName)->info("method", $method)
                ->info("content", $content)
                ->info("ip", $ip)
                ->info("backTime", $backTime)
                ->info("return", $back)
                ->info("requestTime", $requestTime)
                ->info("acceptTime", $this->acceptTime)
                ->info("runTime", $runTime)
                ->write();
    }

}

//spl_autoload_register('BaseController::modelLoader');
