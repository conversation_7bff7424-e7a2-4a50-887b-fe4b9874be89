<?php
/**
 * 主资源配置
 *
 * <AUTHOR>
 */
class Resource {
    public $config  = null;
    public $db      = null;
    
    public function __construct() {
        $this->config   = x5()->import('config/config.ini.php');
    }
    
    public function getDBConnection($nameSpace = 'fzhl_db') {
        if(!isset($this->config[$nameSpace])){
            x5()->output(1002, "Mysql config not found ", __FILE__ . ':' . __LINE__);
        }
        $this->db   = x5()->load('lib.ApiMysql', $this->config[$nameSpace]);
        return $this->db;
    }

    /**
     * Redis连接
     * @param string $nameSpace
     * @return \Redis
     * @throws Exception
     */
    public function getRedis($nameSpace = 'redis') {
        if(!isset($this->config[$nameSpace]))
            throw new Exception("redis config not found ", 1002, __FILE__.':'.__LINE__);
        $redis = new Redis();
        $redis->connect($this->config[$nameSpace]['host'], $this->config[$nameSpace]['port'], 3);
      // $redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_NONE);
        $redis->setOption(0, 0);
        // $redis->auth($this->config[$nameSpace]['namespace']);
        return $redis;
    }


    public function getConfig(){
        return $this->config;
    }

    /**
     * 释放数据库连接
     */
    public function resetResourses() {
        self::$dbTable = array();
    }
    
    public function resetTable() {
        self::$dbTable = array();
    }
}

?>
