<?php

/**
 * 通用日志系统接口
 * 
 * v0.1x：本接口进程间安全，不支持线程安全，如有需求请联系作者
 * 
 * 各种调用方法实例： （各种方法不影响写入效率）
 * 1. 将多次添加的$key,$value打印成一条日志，需要手动调用write参数执行写任务
 *      CommonLog::log()
 *              ->info($key,$value)
 *              ->info($key,$value)
 *              ......
 *              ->write();
 * 
 * 2. 将传入数组中的数据打印为一条数据，无需调用write
 *      CommonLog::log()
 *              ->infoArr($array);
 * 
 * 3. 以进程为单位记录日志，在进程结束时将所有记录打印为一条日志，无需调用write
 *      CommonLog::weblog()->info($key,$value);
 *      .......
 *      CommonLog::weblog()->info($key,$value);
 * 
 * 4. 不同项目记录方式
 *      $path = '/home/<USER>/logs/applogs/ConTest/';
 *      $name = 'test';
 *      $log = CommonLog::log($path,$name);
 *      $log->info($key,$value);
 *      $log->write();
 * 
 * 或者可以编写继承类，详情见下方说明
 * 
 * 
 * 详细说明
 * 
 * 写入日志调用方式 : 
 * 用户自定义字段的名称 $key 以及字段值 $value,字段可以随时添加删除.
 * 1. log() 一次性打印单条日志,通过info()添加参数,添加完成后调用write()写日日志文件中.
 *      调用方式: CommonLog::log()
 *              ->info($key,$value)
 *              ->info($key,$value)
 *              ->write();
 * 2. weblog() 将一次用户访问请求中的多条日志打印在一起,形成一条记录输出到日志文件中.用户的本次访问请求结束后将自动将内容记录到日志文件中.
 *      调用方式: CommonLog::weblog()->info($key,$value);
 *              .......
 *              CommonLog::weblog()->info($key,$value);
 *              .......//程序在结束的时候会自动写入日志文件中,不需要调用write()
 * 添加参数方式 :
 * 1. info($key,$value)
 *      $key作为日志文件的字段名称,$value作为这个字段的值.
 * 2. infoArr($arr)
 *      $arr = array(
 *          $k1 => $v1,
 *          $k2 => $v2,
 *          ...
 *      )
 *      $arr数组中存储了多个$key值不重复的字段的组合,每一组作为一个字段存储在日志文件中.
 * 注意:
 * 1. info/infoArr 中的$key必须可以作为变量名称识别,不然程序会抛出异常.
 * 2. info/infoArr 添加多组值的时候,如果$key出现重复,程序会抛出 'E_USER_NOTICE' 级别的警告,同时这个字段的值会被新的值覆盖.可以通过'@'忽略此警告.
 *      
 * 不同业务如果想分开记日志,可以通过编写子类继承CommonLog实现
 * 例如:
 *      class MyLog extends CommonLog {
 *          protected function __construct() {
 *              $path="/home/<USER>/logs2/";
 *              $logname = 'log';
 *              parent::__construct($path,$logname);
 *          }
 *      }
 * 根据路径的不同便可以将不同业务之间的日志隔离为不同的项目日志
 * 默认路径是 '/home/<USER>/logs/applogs/logWMS/',默认日志文件名称 log.2014-07-21(当天时间)
 * 添加日志路径需要通知 欧纯 <EMAIL>
 * 
 * 扩展:
 * 1. weblog($flag)
 *      在Yii框架下:
 *      通过添加参数 $flag 如 : 
 *      CommonLog::weblog(__FUNCTION__)->info($key,$value);
 *      可以自动记录以下用户的基本信息,方便调用:
 *      info('user_id',Yii::app()->user->id);
 *      info('user_name',Yii::app()->user->name);
 *      info('create_time',time());
 *      info('flag',$flag);
 * 
 *      $flag 参数自定义并且不为 false ,会被作为 flag 参数记录在日志中.
 * 
 * 扩展为方便调用添加,请自行选择.
 * 
 * <AUTHOR>
 * @version 0.128
 */

class CommonLog {
    
    protected $_params = array();
    protected $_path ;
    protected static $_users = array();
    protected static $_check = array();
    protected $_logname;
    protected $_check_authority = true;
    
    protected static $_chunk_size = 8192;
    
    protected function __construct($path="/home/<USER>/logs/applogs/logWMS/",$logname = 'log') {
        $this->_path = $path;
        $this->_logname = $logname;
        if(!is_dir($this->_path)) {
            @mkdir($this->_path,0777,true);
        }
    }
    
    protected function __clone() {}
    
    public static function log($path="/home/<USER>/logs/applogs/logWMS/",$logname = 'log'){
        $class =  get_called_class();
        return new $class($path,$logname);
    }
    
    public static function weblog($flag = false){
        $class =  get_called_class();
        self::$_users[getmypid()] = isset(self::$_users[getmypid()])?self::$_users[getmypid()]:new $class();
        if($flag && !isset(self::$_check[getmypid()])){
            self::$_users[getmypid()]->info('user_id',Yii::app()->user->id);
            self::$_users[getmypid()]->info('user_name',Yii::app()->user->name);
            self::$_users[getmypid()]->info('create_time',time());
            self::$_users[getmypid()]->info('flag',$flag);
            self::$_check[getmypid()] = true;
        }
        return self::$_users[getmypid()];
    }
    
    public function info($key,$value){
        if($this->_check_authority) {
            if(substr($key,0,1) == '$'){
                $key = str_replace('$', '/$', $key);
            }
            $this->_check_authority = $this->_check($key);
            $this->_params[$key] = $value;
        }
        return $this;
    }
    
    public function infoArr($arr){
        if(is_array($arr)){
            foreach($arr as $key => $value){
                $this->_check($key);
                $this->_params[(string)$key] = $value;
            }
            return $this->write();
        }
        trigger_error("params error , '$arr' is not an array",E_USER_NOTICE);
        return false;
    }
    
    public function getparams(){
        return $this->_params;
    }
    
    public function write(){
        if(!empty($this->_params) && $this->_check_authority){
            try {
                $pms = json_encode($this->_params);
                $len = strlen($pms);
                $file = $this->_path.$this->_logname.'.'.date('Y-m-d');
                    
                $bruce=@fopen($file,"a");
                
                if (!is_writable($file)) {
                    return false;
                }

                if($len >= self::$_chunk_size){
                    if (flock($bruce, LOCK_EX)) {
                        fwrite($bruce,  $pms.PHP_EOL);
                        fflush($bruce);
                        flock($bruce, LOCK_UN);
                    } else {
                        trigger_error("Notice :: write failed,can not get the lock",E_USER_NOTICE);
                        return false;
                    }
                    
                } else {
                    fwrite($bruce,  $pms.PHP_EOL);
                }
                fclose($bruce);
                unset($this->_params);
                return true;
            } catch (Exception $e) {
                return false;
            }
        } else {
            return false;
        }
    }
    
    protected function _check($key){
        if(is_numeric($key))
            trigger_error("Params error , '$key' is number , check up your keys",E_USER_NOTICE);
        try{
             if(!@eval("$$key=1;return 1;"))
                 trigger_error("Params error , '$key' can not be used as an variable",E_USER_NOTICE);
             if(isset($this->_params[$key]))
                 trigger_error("Notice :: The key '$key' has already exists and will be covered",E_USER_NOTICE);
        } catch (Exception $e) {
            return false;
        }
        return true;
    }
    
    public function __destruct() {
        if(isset(self::$_users[getmypid()])){
            $this->write();
            unset(self::$_users[getmypid()]);
            unset(self::$_check[getmypid()]);
        }
    }

    public static function sql($sql){
        $app_id = 'xm_0001';
        $app_key = '5b3139a6ab1227fe2293cd26422b141e';
        $url = 'http://log.oc.api.b2c.srv/indexSearch/query';
        $post = json_encode(array($sql));
        $x5 = array(
            'header'=> array(
                'appid'=>$app_id,
                'sign'=>self::sign($app_id, $app_key, $post),
            ), 
            'body'=>$post
        );
        $data = json_encode($x5);
        $result = json_decode(self::postUrl($url, $data),true);
        return $result['body']['list'];
    }
    
    public static function count($sql){
        $app_id = 'xm_0001';
        $app_key = '5b3139a6ab1227fe2293cd26422b141e';
        $url = 'http://log.oc.api.b2c.srv/indexSearch/query';
        $post = json_encode(array($sql));
        $x5 = array(
            'header'=> array(
                'appid'=>$app_id,
                'sign'=>self::sign($app_id, $app_key, $post),
            ), 
            'body'=>$post
        );
        $data = json_encode($x5);
        $result = json_decode(self::postUrl($url, $data),true);
        return $result['body']['count'];
    }
    
    protected static function postUrl($url, $params) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, TRUE);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_TIMEOUT, 200);
        $result = curl_exec($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);
        if ($info['http_code'] != 200) { //判断请求是否成功
            throw new Exception($info['http_code']);
        }
        return $result;
    }

    protected static function sign($app_id, $app_key, $content) {
        return strtoupper(md5($app_id . $content . $app_key));
    }
}

?>
