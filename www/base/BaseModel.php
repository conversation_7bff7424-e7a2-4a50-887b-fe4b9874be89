<?php

/**
 * BaseModel
 * <AUTHOR> <<EMAIL>>
 */
class BaseModel extends Resource {

    public $db;
    protected $table = NULL;  // Model 对应表名
    protected $pk = NULL;  // Model 对应主键
    protected $logId = 99999; // 写 Log 的 operator_id
    protected $logName = 'api'; // 写 Log 的 operator_name
    private static $_models = array();

    public function __construct() {
        parent::__construct();
        if (is_null($this->table) || is_null($this->pk)) {
            x5()->output(500, "Error: table and pk must be set.<br>Class: " . get_called_class());
            exit();
        }

        $this->db = $this->getDBConnection('fzhl_db');
    }
    
    /**
     * 释放数据库连接
     */
    public function resetResourses() {
        $this->__delModel();
        $this->__delDB();
    }
    
    protected function __delModel(){
        self::$_models = array();
    }

    
    protected function __delDB(){
        $this->db = null;
    }

    /**
     * 保存静态实例
     * @param  string $className
     * @return object
     */
    public static function model($className = __CLASS__) {

        if (isset(self::$_models[$className]))
            return self::$_models[$className];
        else {
            $model = self::$_models[$className] = new $className(null);
            return $model;
        }
    }

    /**
     * 根据主键查找
     * @param  int    $id 主键值
     * @param  string $pk 指定主键
     * @param  string $table 指定表名
     * @return array  数据
     */
    public function findByPk($id, $pk = null, $table = null) {
        $pk = !is_null($pk) ? $pk : $this->pk;
        $table = !is_null($table) ? $table : $this->table;
        $sql = "SELECT * FROM `$table` WHERE `$pk` = :id";
        $this->execute($sql, array(':id' => $id));
        return $this->db->getRow();
    }

    /**
     * 根据属性查找所有数据
     * @param  array  $attrs 属性
     * @param  string $table 指定表名
     * @return array  数据
     */
    public function findAllByAttributes($attrs, $table = null) {
        $table = is_null($table) ? $this->table : $table;
        $cond = '1=1';
        foreach ($attrs as $attr => $value) {
            if (is_array($value)) {
                $value = array_map('addslashes', $value);
                $in = implode("','", $value);
                $cond .= " AND `$attr` IN ('$in')";
            } else {
                $value = addslashes(strval($value));
                $cond .= " AND `$attr` = '$value'";
            }
        }
        $sql = "SELECT * FROM `$table` WHERE $cond";
 
        $this->execute($sql);
        return $this->db->getRows();
    }

    /**
     * 根据主键删除数据
     * @param  string $id 主键值
     * @return boolean 操作是否成功
     */
    public function deleteByPk($id) {
        $sql = "DELETE FROM `$this->table` WHERE `$this->pk` = :id";
        return $this->execute($sql, array(':id' => $id));
    }

    /**
     * 根据属性删除所有数据
     * @param  array  $attrs 属性
     * @param  string $table 指定表名
     * @return array  操作是否成功
     */
    public function deleteAllByAttributes($attrs, $table = null) {
        $table = is_null($table) ? $this->table : $table;
        $cond = '1=1';
        foreach ($attrs as $attr => $value) {
            if (is_array($value)) {
                $value = array_map('addslashes', $value);
                $in = implode("','", $value);
                $cond .= " AND `$attr` IN ('$in')";
            } else {
                $value = addslashes(strval($value));
                $cond .= " AND `$attr` = '$value'";
            }
        }
        $sql = "DELETE FROM `$table` WHERE $cond";
        return $this->execute($sql);
    }

    /**
     * 根据属性更新所有数据
     * @param  array  $columns 要更新的列名和值 array('col'=>'value')
     * @param  array  $attrs 属性
     * @param  string $table 指定表名
     * @return array  操作是否成功
     */
    public function updateAllByAttributes($columns, $attrs, $table = null) {
        $table = is_null($table) ? $this->table : $table;

        $sets = array();
        foreach ($columns as $key => $value)
            $sets[] = "`$key` = '$value'";
        $set = implode(',', $sets);

        $cond = '1=1';
        foreach ($attrs as $attr => $value) {
            if (is_array($value)) {
                $value = array_map('addslashes', $value);
                $in = implode("','", $value);
                $cond .= " AND `$attr` IN ('$in')";
            } else {
                $value = addslashes(strval($value));
                $cond .= " AND `$attr` = '$value'";
            }
        }
        $sql = "UPDATE `$table` SET $set WHERE $cond";

        return $this->execute($sql);
    }

    /**
     * 根据属性查询某列的数据
     * @param  array  $columns 要查询的列名
     * @param  array  $attrs 属性
     * @param  string $table 指定表名
     * @return array  操作是否成功
     */
    public function queryColumnByAttributes($column, $attrs, $table = null, $key = false) {
        $table = is_null($table) ? $this->table : $table;

        $cond = '1=1';
        foreach ($attrs as $attr => $value) {
            if (is_array($value)) {
                $value = array_map('addslashes', $value);
                $in = implode("','", $value);
                $cond .= " AND `$attr` IN ('$in')";
            } else {
                $value = addslashes(strval($value));
                $cond .= " AND `$attr` = '$value'";
            }
        }
        
        $sql = "SELECT `$column` FROM `$table` WHERE $cond";
        $this->execute($sql);
        $rows = $this->db->getRows();
        if (!$rows)
            return array();

        $result = array();
        if ($key === true) {
            foreach ($rows as $row)
                $result[$row[$column]] = $row[$column];
        } else {
            foreach ($rows as $row)
                $result[] = $row[$column];
        }

        return $result;
    }

    /**
     * 执行 SQL 返回所有结果
     * @param  string $sql SQL 语句
     * @return array  结果数据
     */
    public function findAllBySql($sql) {
        $this->execute($sql);
        return $this->db->getRows();
    }

    /**
     * 执行 SQL 返回所有结果
     * @param  string $sql SQL 语句
     * @return array  结果数据
     */
    public function findBySql($sql) {
        $this->execute($sql);
        return $this->db->getRow();
    }

    /**
     * 执行 SQL
     * @param  string $sql SQL 语句
     * @param  array  $params 语句参数
     * @return boolean 执行是否成功
     */
    public function execute($sql, $params = array()) {
        return $this->db->sql($sql, $params)->execute();
    }

    public function __call($name, $parameters) {
        x5()->output(500, "Error:" . $name . " mothed is not exists");
        exit();
    }

}