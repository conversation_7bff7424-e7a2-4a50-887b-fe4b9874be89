<?php

/**
 * 通用Mysql数据库操作
 *
 * <AUTHOR>
 */
class CMysql {

    private $db = null;

    public function __construct($db) {
        $this->db = $db;
    }

    public function begin() {
        $this->db->begin();
    }

    public function commit() {
        $this->db->commit();
    }

    public function rollBack() {
        $this->db->rollBack();
    }

    public function getRow($table, $connditions) {
        $rows = $this->getRows($table, $connditions);
        if ($rows)
            return $rows[0];
        else
            return null;
    }

    public function getRowBySql($sql , $binds = array()) {
        $this->db->sql($sql , $binds)->execute();
        return $this->db->getRow();
    }

    public function getRows($table, $connditions) {
        $bindData = $this->_getBindData($connditions);
        $sql = "SELECT * FROM `" . $table . "` WHERE " . implode(' AND ', $bindData[0]);

        $this->db->sql($sql, $bindData[1])->execute();
        $rows = $this->db->getRows();
        return $rows;
    }

    public function getRowsBySql($sql, $binds = array()) {
        $this->db->sql($sql , $binds)->execute();
        return $this->db->getRows();
    }

    public function addRow($table, $data) {
        $bindData = $this->_getBindData($data);
        $sql = "INSERT INTO `" . $table . "` SET " . implode(',', $bindData[0]);
        $res = $this->db->sql($sql, $bindData[1])->execute();
        if($res){//成功为0
                return $this->db->getInsertId();
        }else{
                return false;
        }
    }

    public function addRows($table, $data) {
        $res = true;
        foreach ($data as $value) {
                if (!$this->addRow($table, $value))
                        $res = false;
        }
        return $res;
    }

    public function updateRow($table, $data, $connditions) {
        $bindData = $this->_getBindData($data);
        $bindConn = $this->_getBindData($connditions);
        $sql = "UPDATE `" . $table . "` SET " . implode(',', $bindData[0]) . ' WHERE ' . implode(' AND ', $bindConn[0]);
        $bindParams = array_merge($bindData[1], $bindConn[1]);
        return $this->db->sql($sql, $bindParams)->execute();
    }

    public function deleteRows($table, $connditions) {
        $bindData = $this->_getBindData($connditions);
        $sql = "DELETE FROM `" . $table . "` WHERE " . implode(' AND ', $bindData[0]);
        return $this->db->sql($sql, $bindData[1])->execute();
    }

    public function execute($sql) {
        return $this->db->sql($sql)->execute();
    }

    public function getDbHandle() {
        return $this->db;
    }

    public function reConnect() {
        $this->db->reConnect();
    }

    private function _getBindData($data) {
        $keys = $values = array();
        foreach ($data as $key => $value) {
                $i      = rand(0, 99);  //为防止set status=1 where status=0这种情况出现
                $bindKey = ':' . $key."_".$i;
                $keys[] = '`'.$key . '`=' . $bindKey;
                $values[$bindKey] = $value;
        }
        return array($keys, $values);
    }
}
