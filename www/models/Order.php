<?php
/**
 * Order Service
 */
class Order
{
    private $user_id = 0;
    private $user_name = null;
    private $system_time = null;

	/**
     * 初始化
     */
    public function __construct($params)
    {
        $this->user_id = $params['user_id'];
        $this->user_name = $params['user_name'];
    	$this->system_time = time();
    }

	/**
     * 创建订单
     */
    public function createOrder($data = array())
    {
        //实例化主资源类
        $resource = new Resource();
        //DB
        $db = $resource->getDBConnection();
        $CMysql = x5()->registerModel('CMysql', $db);

        //设备ID
        $machine_id = $data['machine_id'];
        $product_id = $data['product_id'];

        //设备所属商户
        $sql = "select org_id from fzh_org_machine where machine_id = '$machine_id' and status = 1";
        $org_id = $CMysql->getRowBySql($sql)['org_id'];

        //获取最新的产品信息
        $sql = "select product_name,shop_price,time_length from fzh_product where product_id = '$product_id' and status = 1 and del = 0";
        $productRow = $CMysql->getRowBySql($sql);
        if (empty($productRow)) {
            return array("code"=>'400',"info"=>'产品信息不存在');
        }

        //开启事务
        $CMysql->begin();
        try {
            //1.生成订单
            $order_id = Common::getUUID('DD');
            $orderAttr = array(
                'order_id' => $order_id,
                'order_type' => 1,
                'user_id' => $this->user_id,
                'order_status' => 1,
                'org_id' => $org_id ?? 0,
                'machine_id' => $machine_id,
                'product_id' => $product_id,
                'product_name' => $productRow['product_name'],
                'time_length' => $productRow['time_length'],
                'amount' => $productRow['shop_price'],
                'order_amount' => $productRow['shop_price'],
                'pay_status' => 1,
                'create_time' => $this->system_time,
                'update_time' => $this->system_time
            );

            if($orderAttr['order_amount'] < 0){
                $orderAttr['order_amount'] = 0.00;
            }

            $n = $CMysql->addRow('fzh_order', $orderAttr);
            if (!$n) {
                return array("code"=>'400',"info"=>'创建订单失败');
            }

            //2.订单日志
            $log_desc = "创建了订单，订单详情：" . json_encode($orderAttr);
            $order_info = $CMysql->getRow('fzh_order', array('order_id' => $order_id));
            $log_item_after = json_encode($order_info);
            $logAttr = array(
                'order_id' => $order_id,
                'desc' => $log_desc,
                'info_before' => '',
                'info_after' => $log_item_after,
                'is_show' => 1,
                'operator_id' => isset($this->user_id) ? $this->user_id : '0',
                'operator_name' => isset($this->user_name) ? $this->user_name : 'System',
                'create_time' => $this->system_time,
            );
            $CMysql->addRow('fzh_order_log', $logAttr);

            $CMysql->commit();
        } catch (Exception $e) {
            $CMysql->rollback();
            return array("code"=>'400',"info"=>'创建订单失败'.$e->getMessage());
        }
        
        return array("code"=>'200',"info"=>$order_id);
    }
}
