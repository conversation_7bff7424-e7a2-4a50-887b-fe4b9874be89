<?php

return array(
    '1001' => array('info' => 'Method not found', 'desc' => '请求的方法不存在'),
    '1002' => array('info' => 'Mysql config file error', 'desc' => '数据库配置不存在'),
    '1003' => array('info' => 'Sql is null', 'desc' => 'Sql语句为空'),
    '1004' => array('info' => 'Ip is not allowed', 'desc' => 'IP地址没有授权访问'),
    '1005' => array('info' => 'Method is not allowed', 'desc' => '方法没有被授权'),
    '1006' => array('info' => 'Sign check failed', 'desc' => '签名校验失败'),
    '1007' => array('info' => 'Params error', 'desc' => '参数错误'),
    '1008' => array('info' => 'Connect database failed', 'desc' => '数据库连接失败'),
    '1009' => array('info' => 'Undefined error', 'desc' => '未知的异常'),
    
    '1010' => array('info' => 'Appid is not exit', 'desc' => 'appid不是系统指定的'),
    '1011' => array('info' => 'Header is empty', 'desc' => '消息头为空'),
    '1012' => array('info' => 'Body is empty', 'desc' => '消息体为空'),
    '1013' => array('info' => 'Url is empty', 'desc' => '应用请求URL为空'),
    '1014' => array('info' => 'Servicetype is not exit', 'desc' => 'service type服务类型非系统指定'),
    '1015' => array('info' => 'Apitype is not exit', 'desc' => 'api type同异步非系统指定'),
    '1016' => array('info' => 'Appid is empty', 'desc' => 'appid为空'),
    '1017' => array('info' => 'Msgid is empty', 'desc' => '消息体body中必须含有msgid'),
    
    '1018' => array('info' => 'Class not found', 'desc' => '请求的类不存在'),
    '1019' => array('info' => 'Class file not found', 'desc' => '类文件不存在'),

    '2000' => array('info' => 'Query error', 'desc' => '查询/语法错误'),
    '2001' => array('info' => 'Bind error', 'desc' => '预处理绑定错误'),
    '2002' => array('info' => 'Prepare failed', 'desc' => '预处理失败'),
);
?>