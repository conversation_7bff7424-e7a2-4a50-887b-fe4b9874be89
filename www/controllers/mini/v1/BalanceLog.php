<?php

/**
 * BalanceLog 余额记录查询接口
 * <AUTHOR>
 */
class BalanceLog extends BaseController {

    public function run() {
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(401, 'error', "Token cannot be empty");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        //分页参数
        $page = isset($body['page']) ? intval($body['page']) : 1;
        $pageSize = isset($body['pageSize']) ? intval($body['pageSize']) : 20;
        $page = $page < 1 ? 1 : $page;
        $pageSize = $pageSize < 1 ? 20 : ($pageSize > 100 ? 100 : $pageSize);
        $limit_from = ($page - 1) * $pageSize;

        //查询类型筛选
        $change_type = isset($body['change_type']) ? intval($body['change_type']) : 0;
        $where_condition = "user_id = $user_id";
        if($change_type > 0){
            $where_condition .= " and change_type = $change_type";
        }

        //查询总数
        $count_sql = "select count(*) as total from fzh_balance_log where $where_condition";
        $countRow = $CMysql->getRowBySql($count_sql);
        $total = $countRow['total'];

        //查询余额记录列表
        $sql = "select change_type, change_amount, balance_before, balance_after, related_id, create_time, remark 
                from fzh_balance_log 
                where $where_condition 
                order by create_time desc 
                limit $limit_from, $pageSize";
        $logRows = $CMysql->getRowsBySql($sql);

        //格式化数据
        $list = [];
        if (!empty($logRows)) {
            foreach ($logRows as $row) {
                $item = array(
                    'change_type' => intval($row['change_type']),
                    'change_type_text' => $this->getChangeTypeText($row['change_type']),
                    'change_amount' => sprintf("%.2f", floatval($row['change_amount'])),
                    'balance_before' => sprintf("%.2f", floatval($row['balance_before'])),
                    'balance_after' => sprintf("%.2f", floatval($row['balance_after'])),
                    'related_id' => $row['related_id'],
                    'create_time' => date('Y-m-d H:i:s', $row['create_time']),
                    'remark' => $row['remark']
                );
                $list[] = $item;
            }
        }

        $resp = array(
            'list' => $list,
            'page' => $page,
            'pageSize' => $pageSize,
            'total' => intval($total),
            'totalPages' => ceil($total / $pageSize)
        );

        $this->render('200', 'success', $resp);
    }

    /**
     * 获取变动类型文本
     */
    private function getChangeTypeText($change_type) {
        switch ($change_type) {
            case 1:
                return '充值';
            case 2:
                return '支付';
            case 3:
                return '退款';
            default:
                return '未知';
        }
    }
}
