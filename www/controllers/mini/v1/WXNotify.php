<?php
/**
 * WXNotify 统一微信支付回调处理器
 * 支持商品订单和充值订单的支付回调
 * <AUTHOR>
 */

class WXNotify extends BaseController
{
    public function run()
    {
        $time = time();

        //加载配置
        $config = $this->getConfig();
        //加载数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化微信支付类
        $wechatPay = new WechatPay($config['wx_small_config']);

        //使用微信支付类处理回调
        $wechatPay->handleNotify(function($data) use ($CMysql, $time) {
            return $this->processUnifiedNotify($data, $CMysql, $time);
        });
    }

    /**
     * 统一处理微信支付回调业务逻辑
     * 根据订单类型分别处理商品订单和充值订单
     */
    private function processUnifiedNotify($data, $CMysql, $time) {
        $out_trade_no = $data['out_trade_no']; //商户订单号
        $total_fee = $data['total_fee'] / 100; //订单金额
        $result_code = $data['result_code'];

        //处理返回结果
        if ($result_code == 'SUCCESS') {
            //查询订单信息
            $order_result = $CMysql->getRow('fzh_order', array('order_id' => $out_trade_no));
            if (empty($order_result)) {
                return false;
            }

            //检查是否已经处理过
            if ($order_result['order_status'] != '1') {
                return true;
            }

            //验证金额
            if ($total_fee != $order_result['order_amount']) {
                return false;
            }

            //根据订单类型处理不同的业务逻辑
            if ($order_result['order_type'] == 3) {
                // 充值订单
                return $this->processRechargeOrder($order_result, $data, $CMysql, $time);
            } else {
                // 商品订单
                return $this->processProductOrder($order_result, $data, $CMysql, $time);
            }
        }
        return false;
    }

    /**
     * 处理充值订单支付回调
     */
    private function processRechargeOrder($order_result, $data, $CMysql, $time) {
        //开启事务
        $CMysql->begin();
        try {
            // 更新订单状态（公共逻辑）
            $this->updateOrderStatus($order_result['order_id'], $CMysql, $time);

            // 充值特有逻辑：更新用户余额
            $this->updateUserBalance($order_result, $CMysql, $time);

            // 记录支付日志（公共逻辑）
            $this->recordPaymentLog($order_result['order_id'], $data, $CMysql, $time);

            $CMysql->commit();
            return true;

        } catch (Exception $exception) {
            $CMysql->rollback();
            error_log('充值回调处理异常: ' . $exception->getMessage());
            return false;
        }
    }

    /**
     * 处理商品订单支付回调
     */
    private function processProductOrder($order_result, $data, $CMysql, $time) {
        try {
            // 更新订单状态（公共逻辑）
            $this->updateOrderStatus($order_result['order_id'], $CMysql, $time);

            // 商品订单特有逻辑：记录订单日志
            $this->recordOrderLog($order_result['order_id'], $data, $CMysql, $time);

            // 记录支付日志（公共逻辑）
            $this->recordPaymentLog($order_result['order_id'], $data, $CMysql, $time);

            return true;

        } catch (Exception $exception) {
            error_log('商品订单回调处理异常: ' . $exception->getMessage());
            return false;
        }
    }

    /**
     * 更新订单状态（公共方法）
     */
    private function updateOrderStatus($order_id, $CMysql, $time) {
        $updateData = array(
            'order_status' => 5, // 已付款
            'pay_id' => 1, // 微信支付
            'pay_status' => 5, // 支付成功
            'pay_time' => $time,
            'update_time' => $time
        );
        $CMysql->updateRow('fzh_order', $updateData, array('order_id' => $order_id));
    }

    /**
     * 更新用户余额（充值订单专用）
     */
    private function updateUserBalance($order_result, $CMysql, $time) {
        $user_id = $order_result['user_id'];
        $order_id = $order_result['order_id'];
        $recharge_amount = $order_result['order_amount'];

        // 获取用户当前余额
        $userRow = $CMysql->getRow('fzh_user', array('user_id' => $user_id));
        if(empty($userRow)){
            throw new Exception('用户不存在');
        }

        $balance_before = isset($userRow['balance']) ? floatval($userRow['balance']) : 0.00;
        $balance_after = $balance_before + $recharge_amount;

        // 更新用户余额
        $CMysql->updateRow('fzh_user',
            array('balance' => $balance_after),
            array('user_id' => $user_id)
        );

        // 记录余额变动日志
        $balanceLogAttr = array(
            'user_id' => $user_id,
            'change_type' => 1, // 1-充值
            'change_amount' => $recharge_amount,
            'balance_before' => $balance_before,
            'balance_after' => $balance_after,
            'related_id' => $order_id,
            'create_time' => $time,
            'remark' => '充值成功，充值金额：' . $recharge_amount . '元'
        );
        $CMysql->addRow('fzh_balance_log', $balanceLogAttr);
    }

    /**
     * 记录订单日志（商品订单专用）
     */
    private function recordOrderLog($order_id, $data, $CMysql, $time) {
        $total_fee = $data['total_fee'] / 100;

        $logAttr = array(
            'order_id'      => $order_id,
            'desc'          => "订单通过微信付款，付款金额：" . $total_fee . "元",
            'create_time'    => $time,
            'info_before'   => '待付款',
            'info_after'    => '已付款',
            'is_show'       => 1,
            'operator_id'   => '100',
            'operator_name' => 'System',
        );
        $CMysql->addRow('fzh_order_log', $logAttr);
    }

    /**
     * 记录支付日志（公共方法）
     */
    private function recordPaymentLog($order_id, $data, $CMysql, $time) {
        $total_fee = $data['total_fee'] / 100;
        $transaction_id = $data['transaction_id'];

        $payLogAttr = array(
            'pay_id' => 1,
            'order_id' => $order_id,
            'transaction_id' => $transaction_id,
            'pay_amount' => $total_fee,
            'code' => $data['result_code'],
            'response' => json_encode($data),
            'create_time' => $time,
        );
        $CMysql->addRow('fzh_notify_paylog', $payLogAttr);
    }
}
