<?php

/**
 * 用户协议、隐私政策
 * <AUTHOR>
 */
class Agreement extends BaseController
{
    public function run()
    {
        $resp = [];

        //加载配置
        $CMysql = x5()->registerModel('CMysql', $this->db);

        //用户协议、隐私政策
        $sql = "select * from fzh_site";
        $siteRow = $CMysql->getRowBySql($sql);
        $resp['user_agreement'] = $siteRow['user_agreement'];
        $resp['privacy_policy'] = $siteRow['privacy_policy'];

        $this->render('200', 'success', $resp);
    }
}
