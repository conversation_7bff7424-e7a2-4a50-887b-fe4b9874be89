<?php

/**
 * 退出登录
 * <AUTHOR>
 */
class Logout extends BaseController {

    public function run() {

        //实例化redis
        $redis = $this->getRedis('redis');
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        
        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);
        
        //接收条件
        if(empty($body['token']) || !isset($body['token'])){
            $this->render('401', 'error', 'Token cannot be empty');
        }
        $token = $body['token'];

        $redis->set($token,'');

        $this->render(200, 'success', 'Operation succeeded');
    }

}
