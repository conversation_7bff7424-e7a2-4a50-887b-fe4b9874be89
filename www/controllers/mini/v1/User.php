<?php

/**
 * 个人中心
 * <AUTHOR>
 */
class User extends BaseController
{
    public function run()
    {
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(401, 'error', "Token cannot be empty");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        //公共条件
        $cond = "user_id = $user_id and del = 0";
        //获取用户信息包括余额
        $sql = "select user_id,user_name,tel,balance from fzh_user where " . $cond;
        $userRow = $CMysql->getRowBySql($sql);

        //格式化余额显示
        if(!empty($userRow)){
            $userRow['balance'] = isset($userRow['balance']) ? sprintf("%.2f", floatval($userRow['balance'])) : "0.00";
        }

        $this->render('200', 'success', $userRow ?? []);
    }
}
