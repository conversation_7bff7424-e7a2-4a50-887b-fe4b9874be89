<?php

/**
 * 下单接口
 * <AUTHOR>
 */
class OrderCreate extends BaseController
{
    public function run()
    {
        $time = time();
        $resp = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        /*
        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if(!isset($body['token']) || empty($body['token'])){
            $this->render(401, 'error', "Token cannot be empty");
        } 
        
        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        //商品参数
        if(!isset($body['product_id']) || empty($body['product_id'])){
            $this->render(401, 'error', "Product cannot be empty");
        }
        
        //设备ID
        if(!isset($body['machine_id']) || empty($body['machine_id'])){
            $this->render(401, 'error', "machine_id cannot be empty");
        }

        //创建订单
        $data['user_id'] = $user_id;
        $data['user_name'] = $user['user_name'];
        */
        $data = [
            'user_id' => 10110002,
            'user_name' => '13321123653',
        ];
        $body = [
            'product_id' => '100001',
            'machine_id' => '10203000',
        ];
        $Order = x5()->registerModel('Order', $data);
        $result = $Order->createOrder($body);
        if(!empty($result)){
            if($result['code'] == '200'){
                $order_id = $result['info'];
                $omap['order_id'] = $order_id;
                $orderAttr = $CMysql->getRow('fzh_order',$omap);
                if(!empty($orderAttr)){
                    //成功以后的返回
                    $resp = array(
                        'order_id' => $order_id,
                    );
                }
                $this->render(200, "success", $resp);
            }else{
                $this->render(400, "error", $result['info']);
            }
        }else{
            $this->render(400, "error", "下单失败");
        }
    }
}
