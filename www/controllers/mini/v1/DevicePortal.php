<?php

/**
 * 设备控制接口
 * <AUTHOR>
 */
class DevicePortal extends BaseController
{
    public function run()
    {
        $resp = [];

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(401, 'error', "Token cannot be empty");
        }
        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        //设备ID
        if (!isset($body['machine_id']) || empty($body['machine_id'])) {
            $this->render(401, 'error', "machine id cannot be empty");
        }
        $machine_id = Common::xss_clean($body['machine_id']);
        $machine_id = '10203000';

        //用户可用余额
        $sql = "select balance from fzh_user where user_id = '$user_id'";
        $balance = $CMysql->getRowBySql($sql)['balance'];

        // 获取最新的设备信息
        $condition = array('id' => $machine_id);
        $machineRow = $CMysql->getRow('fzh_machine', $condition);
        if (empty($machineRow)) {
            $this->render(401, 'error', "machine info cannot be empty");
        }
        if ($machineRow['status'] != 1) {
            $this->render(401, 'error', "设备处于禁用状态，不可以使用");
        }

        //查询可用产品信息,目前不知道是否产品和设备需要绑定，先把所有可用得产品读出来
        $field = "product_id,cat_id,product_name,product_title,product_desc,shop_price,time_length";
        $sql = "select $field from fzh_product where del = 0 and status = 1";
        $productRows = $CMysql->getRowsBySql($sql);

        //余额
        $resp['balance'] = $balance;
        //设备信息
        $resp['machineRow'] = $machineRow;
        // 产品信息
        $resp['productRows'] = $productRows ?? [];
        // 使用说明
        $resp['use_instructions'] = [
            '首次使用建议选择0元测试套餐体验效果',
            '理疗过程中若有任何不适，请立即停止并联系客服',
            '建议每日理疗时间不超过2小时',
            '孕妇、心脏病患者等特殊人群请咨询医生后使用',
        ];
        // 客服电话
        $resp['customer_service_phone'] = $config['customer_service_phone'];
        $this->render(200, "success", $resp);
    }
}
