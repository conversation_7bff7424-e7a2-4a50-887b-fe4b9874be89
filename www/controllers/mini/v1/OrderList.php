<?php

/**
 * 订单列表接口
 * 支持根据订单状态和订单类型进行筛选
 * <AUTHOR>
 */

class OrderList extends BaseController
{
    public function run()
    {
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(401, 'error', "Token cannot be empty");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "The token has expired. Please log in again");
        }
        $user_id = $user['user_id'];

        // 获取筛选参数
        $order_status = isset($body['order_status']) ? intval($body['order_status']) : 0; // 0:全部, 1:待付款, 6:使用中, 9:已完成
        $order_type = isset($body['order_type']) ? intval($body['order_type']) : 0; // 0:全部, 1:消费订单, 3:充值订单

        // 分页参数
        $pageNo = isset($body['pageNo']) && !empty($body['pageNo']) ? intval($body['pageNo']) : 1;
        $pageSize = isset($body['pageSize']) && !empty($body['pageSize']) ? intval($body['pageSize']) : 10;
        $limit_from = ($pageNo - 1) * $pageSize;

        // 构建查询条件
        $where_conditions = [];
        $where_conditions[] = "del = 0";
        $where_conditions[] = "user_id = $user_id";

        // 订单状态筛选
        if ($order_status > 0) {
            $where_conditions[] = "order_status = $order_status";
        }

        // 订单类型筛选
        if ($order_type > 0) {
            $where_conditions[] = "order_type = $order_type";
        }

        $where_clause = implode(' AND ', $where_conditions);

        // 获取各状态订单数量统计
        $stats = $this->getOrderStats($CMysql, $user_id);

        // 获取订单列表
        $sql = "SELECT order_id, order_type, order_status, org_id, machine_id, product_id, product_name,
                       time_length, amount, order_amount, pay_id, pay_status, create_time, update_time
                FROM fzh_order
                WHERE $where_clause
                ORDER BY create_time DESC
                LIMIT $limit_from, $pageSize";

        $orderRows = $CMysql->getRowsBySql($sql);
        $list = [];

        if (!empty($orderRows)) {
            foreach ($orderRows as $order) {
                // 根据订单状态显示不同的状态文本和颜色
                $statusInfo = $this->getOrderStatusInfo($order['order_status']);

                $orderItem = [
                    'order_id' => $order['order_id'],
                    'order_type' => $order['order_type'],
                    'order_type_text' => $this->getOrderTypeText($order['order_type']),
                    'order_status' => $order['order_status'],
                    'order_status_text' => $statusInfo['text'],
                    'order_status_color' => $statusInfo['color'],
                    'product_name' => $order['product_name'] ?: $this->getDefaultProductName($order['order_type']),
                    'time_length' => $order['time_length'],
                    'order_amount' => number_format($order['order_amount'], 2),
                    'amount' => number_format($order['amount'], 2),
                    'pay_status' => $order['pay_status'],
                    'pay_status_text' => $this->getPayStatusText($order['pay_status']),
                    'create_time' => date('Y-m-d H:i:s', $order['create_time']),
                    'create_time_format' => date('Y-m-d H:i:s', $order['create_time'])
                ];

                $list[] = $orderItem;
            }
        }

        // 构建响应数据
        $resp = [
            'stats' => $stats,
            'list' => $list,
            'pagination' => [
                'current_page' => $pageNo,
                'page_size' => $pageSize,
                'total' => $stats['total']
            ]
        ];

        $this->render(200, 'success', $resp);
    }

    /**
     * 获取各状态订单数量统计
     */
    private function getOrderStats($CMysql, $user_id) {
        $base_condition = "del = 0 AND user_id = $user_id";

        // 总订单数
        $sql = "SELECT COUNT(*) as num FROM fzh_order WHERE $base_condition";
        $total = $CMysql->getRowBySql($sql)['num'];

        // 待付款订单数 (order_status = 1)
        $sql = "SELECT COUNT(*) as num FROM fzh_order WHERE $base_condition AND order_status = 1";
        $pending_payment = $CMysql->getRowBySql($sql)['num'];

        // 使用中订单数 (order_status = 6)
        $sql = "SELECT COUNT(*) as num FROM fzh_order WHERE $base_condition AND order_status = 6";
        $in_use = $CMysql->getRowBySql($sql)['num'];

        // 已完成订单数 (order_status = 9)
        $sql = "SELECT COUNT(*) as num FROM fzh_order WHERE $base_condition AND order_status = 9";
        $completed = $CMysql->getRowBySql($sql)['num'];

        return [
            'total' => $total,
            'pending_payment' => $pending_payment,
            'in_use' => $in_use,
            'completed' => $completed
        ];
    }

    /**
     * 获取订单状态信息（包含文本和颜色）
     */
    private function getOrderStatusInfo($order_status) {
        switch ($order_status) {
            case 1:
                return ['text' => '待付款', 'color' => '#FF6B35'];
            case 5:
                return ['text' => '已支付', 'color' => '#4CAF50'];
            case 6:
                return ['text' => '使用中', 'color' => '#2196F3'];
            case 7:
                return ['text' => '超时', 'color' => '#FF5722'];
            case 9:
                return ['text' => '已完成', 'color' => '#4CAF50'];
            default:
                return ['text' => '未知状态', 'color' => '#999999'];
        }
    }

    /**
     * 获取订单类型文本
     */
    private function getOrderTypeText($order_type) {
        switch ($order_type) {
            case 1:
                return '消费订单';
            case 3:
                return '充值订单';
            default:
                return '未知订单';
        }
    }

    /**
     * 获取默认产品名称
     */
    private function getDefaultProductName($order_type) {
        switch ($order_type) {
            case 1:
                return '服务套餐';
            case 3:
                return '账户充值';
            default:
                return '未知产品';
        }
    }

    /**
     * 获取支付状态文本
     */
    private function getPayStatusText($pay_status) {
        switch ($pay_status) {
            case 1:
                return '未支付';
            case 5:
                return '已支付';
            default:
                return '未知状态';
        }
    }
}
