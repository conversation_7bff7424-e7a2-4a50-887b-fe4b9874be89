<?php

/**
 * PayStatus  //查询订单状态
 * <AUTHOR>
 */
class PayStatus extends BaseController
{
    public function run()
    {
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化Redis
        $redis = $this->getRedis('redis');

        //获取参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(400, 'error', "token不能为空!");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "token已过期,请重新登录!");
        }

        $order_id = $body['order_id'] ?? '';
        if (empty($order_id)) {
            $this->render(401, 'error', "order_id不能为空");
        }
        $sql = "select order_status from fzh_order where order_id = '$order_id'";
        $orderRow = $CMysql->getRowBySql($sql);
        $this->render(200, 'success', $orderRow);
    }
}
