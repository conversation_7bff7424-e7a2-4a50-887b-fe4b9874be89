<?php

/**
 * 校验账号状态是否正常
 * <AUTHOR>
 */
class checkToken extends BaseController
{
    public function run()
    {
        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化Redis
        $redis = $this->getRedis('redis');

        //获取参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //验证是否登陆，没登陆返回错误跳转到登录页
        if (!isset($body['token']) || empty($body['token'])) {
            $this->render(400, 'error', "token不能为空!");
        }

        //判断token是否过期
        $token = $body['token'];
        $user = json_decode($redis->get($token), true);
        if (empty($user)) {
            $this->render(401, 'error', "token已过期,请重新登录!");
        }

        $userRow = $CMysql->getRow('fzh_user', array('user_id' => $user['user_id']));

        if ($userRow['status'] != 1) {
            $redis->set($token, '');
        }

        $this->render(200, 'success', 'Operation succeeded');
    }
}
