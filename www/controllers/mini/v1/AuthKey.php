<?php

/**
 * <PERSON>th<PERSON><PERSON>
 * <AUTHOR>
 */
class Auth<PERSON>ey extends BaseController
{

    public function run()
    {
        $time = time();

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //获取参数
        if (!isset($body['code']) && empty($body['code'])) {
            $this->render(400, 'error', "Code cannot be empty");
        }
        $code = $body['code'];

        //1、小程序获取用户openid
        $appid = $config['wx_small_config']['wx_app_id'];
        $secret = $config['wx_small_config']['wx_app_secret'];
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid=$appid&secret=$secret&js_code=$code&grant_type=authorization_code";
        $info = file_get_contents($url);
        $json = json_decode($info);
        $arr = get_object_vars($json);
        if (!isset($arr['openid']) || empty($arr['openid'])) {
            $this->render(400, 'error', $arr['errmsg']);
        }
        $openid = $arr['openid'];
        $session_key = $arr['session_key'];

        $attr['openid'] = $openid;
        $attr['session_key'] = $session_key;
        //2、session_key保存到Redis
        $secret_key = Common::_3rd_session(16);
        $redis->set($secret_key, json_encode($attr));

        $resp = array(
            'secret_key' => $secret_key
        );

        $this->render('200', 'success', $resp);
    }
}
