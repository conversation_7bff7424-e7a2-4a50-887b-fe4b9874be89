<?php

/**
 * Auth
 * <AUTHOR>
 */
class Auth extends BaseController
{

    public function run()
    {
        $time = time();

        //加载配置
        $config = $this->getConfig();
        //实例化数据库操作类
        $CMysql = x5()->registerModel('CMysql', $this->db);
        //实例化redis
        $redis = $this->getRedis('redis');

        //接收参数
        $params = x5()->getParams('post');
        $body = $this->getBody($params);

        //获取参数
        if (!isset($body['secret_key']) && empty($body['secret_key'])) {
            $this->render(400, 'error', "Secret key cannot be empty");
        }
        $secret_key = $body['secret_key'];
        if (!isset($body['iv']) && empty($body['iv'])) {
            $this->render(400, 'error', "Iv cannot be empty");
        }
        $iv = $body['iv'];
        if (!isset($body['encryptedData']) && empty($body['encryptedData'])) {
            $this->render(400, 'error', "encryptedData cannot be empty");
        }
        $encryptedData = $body['encryptedData'];

        //1、获取session_key
        $secretAttr = json_decode($redis->get($secret_key), true);
        if (empty($secretAttr['session_key'])) {
            $this->render('411', 'Get session key failed', '');
        }
        $session_key = $secretAttr['session_key'];
        $openid = $secretAttr['openid'];

        //2、解析小程序加密数据，获取手机号
        //实例化微信解密类
        x5()->import('lib/wxsdk/wxBizDataCrypt.php');
        //解析微信加密数据，获取手机号
        $appid = $config['wx_small_config']['wx_app_id'];
        $pc = new WXBizDataCrypt($appid, $session_key);
        $errCode = $pc->decryptData($encryptedData, $iv, $data);
        if ($errCode != 0) {
            $this->render(411, 'error', "Parsing failed");
        }
        $attr = json_decode($data, true);
        $phoneNumber = $attr['phoneNumber'];

        //3、生成token
        $token = Common::getToken($openid);
        //4、查询用户是否存在
        $condition = array('tel' => $phoneNumber);
        $user_info = $CMysql->getRow('fzh_user', $condition);
        if (!empty($user_info)) {
            $uptAttr['last_visit'] = $time;
            $uptAttr['token'] = $token;
            $uptAttr['openid'] = $openid;
            $s = $CMysql->updateRow('fzh_user', $uptAttr, $condition);
            if (!$s) {
                $this->render('400', 'Login failed', '');
            }
        } else {
            $userAttr['user_name'] = $phoneNumber;
            $userAttr['openid'] = $openid;
            $userAttr['tel'] = $phoneNumber;
            $userAttr['token'] = $token;
            $userAttr['ip'] = Common::getIP();
            $userAttr['last_visit'] = $userAttr['create_time'] = $userAttr['update_time'] = $time;
            $n = $CMysql->addRow('fzh_user', $userAttr);
            if (!$n) {
                $this->render('400', 'Login failed', '');
            }
        }

        //5、获取最新的用户信息
        $user_info = $CMysql->getRow('fzh_user', $condition);
        if ($user_info['status'] != 1) {
            $this->render("400", "账号已被禁用，请联系管理员", "");
        }
        $resp = array(
            'user_id' => $user_info['user_id'],
            'user_name' => $user_info['user_name'],
            'tel' => $user_info['tel'],
            'token' => $user_info['token'],
            'openid' => $user_info['openid']
        );
        //6、用户信息保存到Redis
        $redis->set($token, json_encode($resp));
        $redis->expire($token, 86400 * 30);

        $this->render('200', 'Login success', $resp);
    }
}
