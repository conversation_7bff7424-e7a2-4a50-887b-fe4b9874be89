<?php

/**
 * X5引擎，专为对接第三方api设计
 *
 * <AUTHOR>
 */
interface X5_api {

    public function run();
}

class X5 implements X5_api {

    private $nameSpace = null;

    public function __construct() {
        $uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        if (($pos = strpos($uri, '?')) !== FALSE) {
            $uri = substr($uri, 0, $pos);
        }
        $this->nameSpace = 'controllers' . str_replace('/', '.', $uri);
    }

    public function run() {
        $obj = $this->load($this->nameSpace);
        $obj->run();
    }

    public function runc() {
//restore_error_handler();
//restore_exception_handler();
        set_time_limit(0);
        set_exception_handler(array('X5', 'exceptionHandlerCommand'));
        if (!isset($_SERVER['argv'][1])) {
            if (function_exists('scandir')) {
                $list = scandir(APP_PATH . '/commands/');
                echo "--------------------- You can call these classes:--------------------------\r\n";
                foreach ($list as $key => $value) {
                    if (strstr($value, 'Command')) {
                        $command = str_replace('Command.php', '', $value);
                        echo $command . "\r\n";
                    }
                }
                echo "--------------------- the end -------------------------------------------\r\n";
            }
            throw new Exception("Class param is not null", 1018);
        }
        $classFile = ucwords($_SERVER['argv'][1]) . 'Command.php';
        $classPath = APP_PATH . '/commands/' . $classFile;
        if (!file_exists($classPath))
            throw new Exception("Class file " . $classFile . " is not exists", 1018);
        include_once $classPath;
        $className = $_SERVER['argv'][1] . 'Command';
        if (class_exists($className))
            $class = new $className;
        else
            throw new Exception("Unable to load class " . $className, 1001);

        if (!isset($_SERVER['argv'][2])) {

            if (function_exists('scandir')) {
                $class_methods = get_class_methods($className);
                echo "--------------------- You can call these methods:--------------------------\r\n";
                foreach ($class_methods as $key => $value) {
                    if (strstr($value, 'do') !== false) {
                        $method = str_replace('do', '', $value);
                        echo "x5c.php " . str_replace('Command', '', $className) . " {$method} \r\n";
                    }
                }
                echo "--------------------- the end -------------------------------------------\r\n\r\n";
            }

            throw new Exception("Method param is not null", 1001);
        }

        $method = 'do' . ucwords($_SERVER['argv'][2]);
        if (!method_exists($class, $method))
            throw new Exception("Unable to run method " . $method, 1001);
        $parameters = $_SERVER['argv'];
        unset($parameters[0], $parameters[1], $parameters[2]);
        call_user_func_array(array($class, $method), $parameters);
    }

    static function output($code, $msg, $location = "", $body = "", $format = 'json') {
//header('Content-type:text/html; charset=utf-8');
        header('Access-Control-Allow-Origin:*');  
        if ($format === 'json') {
            $ret = array(
                'header' => array(
                    'code' => $code,
                    'desc' => $msg,
                ),
                'body' => $body,
            );
            if (defined('API_DEBUG') and API_DEBUG)
                $ret['header']['location'] = $location;
            echo json_encode($ret);
        }elseif ($format === 'txt') {
            echo $msg;
        }
//exit;
    }

    public function load($nameSpace, $params = '') {
        $arr = explode('.', $nameSpace);
        $class = APP_PATH . '/' . implode("/", $arr) . '.php';
        if (!file_exists($class)) {
            throw new Exception($nameSpace . " not found1".$class, 1019);
        }
        include_once $class;
        $className = $arr[count($arr) - 1];
        if (!class_exists($className)) {
            throw new Exception($class . " not found", 1018);
        }
        return new $className($params);
    }

    public static function import($path) {
        $file = APP_PATH . '/' . $path;
        if (!file_exists($file)) {
            throw new Exception($file . " not found2".$file, 1019);
        }
        return include $file;
    }

    public static function loadComponents($className) {
        $class = APP_PATH . '/base/' . $className . '.php';
        if (!file_exists($class)){
            throw new Exception($className . " not found3".$class, 1019);
        }else{
            return include_once $class;
        }

    }

    public function getParams($method = 'post') {
        if ($method === 'post') {
            $content = '';
            if (isset($_POST['data']))
                $content = $_POST['data'];
            else
                $content = file_get_contents('php://input', 'r');
            if (empty($content))
                throw new Exception("Param not found \$_POST['data']", 1007);
            return json_decode(base64_decode($content), TRUE);
        }else {
            if (!isset($_GET['data']))
                throw new Exception("Params not found \$_GET['data']", 1007);
            return json_decode(base64_decode($_GET['data']), TRUE);
        }
    }

    public function registerModel($model, $params = null) {
        try {
            $nameSpace = 'models.' . str_replace('/', '.', $model);
            return $this->load($nameSpace, $params);
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), $e->getCode());
        }
    }

    public function sendRequest($reqUrl, $sendParams, $timeout = 5) {
        $params = array();
        $encRequest = base64_encode(json_encode($sendParams));
        $params['data'] = $encRequest;

//initialize and setup the curl handler
        $ch = curl_init();
        $options = array(
            CURLOPT_URL => $reqUrl,
            CURLOPT_POST => TRUE,
            CURLOPT_POSTFIELDS => $params,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_USERAGENT => 'XM_X5',
        );
        curl_setopt_array($ch, $options);
//execute the request
        $result = curl_exec($ch);
        if ($errno = curl_errno($ch))
            throw new Exception("Send request error:" . curl_error($ch), 1009);
        return $result;
    }

    public function auth($param) {
        if (empty($param))
            throw new Exception("param is empty", 1007);
//error_log(var_export($param, TRUE), 3, '/tmp/tmp.txt');
        include APP_PATH . '/config/apps.ini.php';

        if (!array_key_exists('header', $param)) {
            throw new Exception("header is empty", 1011);
        }

        if (!array_key_exists('body', $param)) {
            throw new Exception("body is empty", 1012);
        }

        if (!array_key_exists('appid', $param['header'])) {
            throw new Exception("appid is empty", 1016);
        }
        $appid = $param['header']['appid'];
//ip验证
        $ipcks = false;
        $currentIP = self::getIP();
        if (array_key_exists($appid, $appsList)) {
            foreach ($appsList[$appid]['iplist'] as $key => $value) {
                if ($value === '*')
                    $ipcks = true;
                if (stripos($currentIP, str_replace('*', '', $value)) !== false) {
                    $ipcks = true;
                } else {
                    continue;
                }
            }
        } else {
            throw new Exception("appid " . $appid . " is not exist", 1010);
        }


        if (!$ipcks)
            throw new Exception("ip " . $currentIP . " is not allowed");
//方法验证
        $methods = false;
        foreach ($appsList[$appid]['methodList'] as $key => $value) {
            if ($value === '*')
                $methods = true;
            if (stripos($this->nameSpace, str_replace('*', '', $value)) !== false) {
                $methods = true;
            } else {
                continue;
            }
        }
        if (!$methods)
            throw new Exception("Method " . $this->nameSpace . " is not allowed", 1005);
//sign验证
        $ckString = $appid . $param['body'] . $appsList[$appid]['key'];
        $sign = strtoupper(md5($ckString));
        if ($sign != $param['header']['sign'])
            throw new Exception("Sign check failed: " . $ckString, 1006);
        return true;
    }

    static public function errorHandler($errno, $errstr, $errfile, $errline) {
        throw new ErrorException($errstr, 0, $errno, $errfile, $errline);
    }

    static public function exceptionHandler($e, $command = false) {
        $errorCode = $e->getCode();
        if ($errorCode < 10000)
            $errorCodes = self::import('config/errorsys.ini.php');
        else
            $errorCodes = self::import('config/errorapp.ini.php');
        if (!isset($errorCodes[$errorCode])) {
            $errorCode = $errorCode;
            $errorMsg = $e->getMessage();
        } else {
            $errorMsg = $errorCodes[$errorCode]['desc'];
        }

        if ($command) {
            echo "ErrorCode:" . $errorCode . "\n";
            echo "ErrorDesc:" . $errorMsg . "\n";
            echo "ErrorBody:" . $e->getFile() . ':' . $e->getLine() . ':' . $e->getMessage() . "\n";
        } else {
            self::output($errorCode, $errorMsg, $e->getFile() . ':' . $e->getLine() . ':' . $e->getMessage());
        }
    }

    static public function exceptionHandlerCommand($e) {
        self::exceptionHandler($e, true);
    }

    public static function getIP() {
        # 前端服务器使用了nginx proxy ，必须才能获取外网的真实IP
        if (!empty($_SERVER["HTTP_X_FORWARDED_FOR"]))
            $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        else if (!empty($_SERVER["REMOTE_ADDR"]))
            $ip = $_SERVER["REMOTE_ADDR"];
        else
            $ip = "Unknown";
        return $ip;
    }

}

set_error_handler(array('X5', 'errorHandler'));
set_exception_handler(array('X5', 'exceptionHandler'));

function x5() {
    static $instance = null;
    if ($instance == null)
        $instance = new X5();
    return $instance;
}

spl_autoload_register(array('x5', 'loadComponents'));
?>