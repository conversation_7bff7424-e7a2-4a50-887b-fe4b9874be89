<?php

/**
 * 微信支付公共类
 * <AUTHOR>
 */
class WechatPay {
    
    private $config;
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    /**
     * 统一下单接口
     * @param string $product_name 商品名称
     * @param string $order_no 订单号
     * @param float $total_amount 支付金额（元）
     * @param string $openid 用户openid
     * @param string $notify_url 回调地址
     * @return array 支付参数
     */
    public function createOrder($product_name, $order_no, $total_amount, $openid, $notify_url = null) {
        $time = time();
        $total_fee = intval($total_amount * 100); // 转换为分
        
        // 使用默认回调地址或指定的回调地址
        $notify_url = $notify_url ?: $this->config['notify'];
        
        // 获取预支付ID
        $prepay_id = $this->generatePrepayId($product_name, $order_no, $total_fee, $openid, $notify_url);
        if (!$prepay_id) {
            throw new Exception('获取预支付ID失败');
        }
        
        // 生成支付参数
        $payment_params = array(
            'appId' => $this->config['wx_app_id'],
            'package' => "prepay_id=$prepay_id",
            'signType' => "MD5",
            'nonceStr' => $this->generateNonce(),
            'timeStamp' => "$time",
        );
        
        // 生成支付签名
        $payment_params['paySign'] = $this->calculateSign($payment_params, $this->config['wx_app_key']);
        
        return $payment_params;
    }
    
    /**
     * 生成预支付ID
     */
    private function generatePrepayId($product_name, $order_no, $total_fee, $openid, $notify_url) {
        $params = array(
            'appid' => $this->config['wx_app_id'],
            'mch_id' => $this->config['wx_mch_id'],
            'nonce_str' => $this->generateNonce(),
            'body' => $product_name,
            'out_trade_no' => $order_no,
            'total_fee' => $total_fee,
            'spbill_create_ip' => $this->getClientIp(),
            'notify_url' => $notify_url,
            'trade_type' => 'JSAPI',
            'openid' => $openid,
        );
        
        // 生成签名
        $params['sign'] = $this->calculateSign($params, $this->config['wx_app_key']);
        
        // 创建XML
        $xml = $this->arrayToXml($params);
        
        // 发送请求
        $result = $this->sendRequest('https://api.mch.weixin.qq.com/pay/unifiedorder', $xml);
        
        // 解析响应
        $response = simplexml_load_string($result);
        if ((string)$response->return_code === 'SUCCESS' && (string)$response->result_code === 'SUCCESS') {
            return (string)$response->prepay_id;
        }
        
        return false;
    }
    
    /**
     * 计算签名
     */
    public function calculateSign($params, $key) {
        // 过滤空值并排序
        $filtered_params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        ksort($filtered_params);
        
        // 生成签名字符串
        $string = '';
        foreach ($filtered_params as $k => $v) {
            $string .= $k . '=' . $v . '&';
        }
        $string = $string . 'key=' . $key;
        
        return strtoupper(md5($string));
    }
    
    /**
     * 验证回调签名
     */
    public function verifyNotifySign($data) {
        if (!isset($data['sign'])) {
            return false;
        }
        
        $sign = $data['sign'];
        unset($data['sign']);
        
        $calculated_sign = $this->calculateSign($data, $this->config['wx_app_key']);
        
        return $sign === $calculated_sign;
    }
    
    /**
     * 数组转XML
     */
    private function arrayToXml($params) {
        $xml = '<xml>';
        foreach ($params as $key => $value) {
            if (is_numeric($value)) {
                $xml .= "<{$key}>{$value}</{$key}>";
            } else {
                $xml .= "<{$key}><![CDATA[{$value}]]></{$key}>";
            }
        }
        $xml .= '</xml>';
        return $xml;
    }
    
    /**
     * XML转数组
     */
    public function xmlToArray($xml) {
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendRequest($url, $data) {
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => array('Content-Type: text/xml'),
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ));
        
        $result = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('请求失败: ' . $error);
        }
        
        return $result;
    }
    
    /**
     * 生成随机字符串
     */
    public function generateNonce($length = 32) {
        return md5(uniqid('', true));
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        if (!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) {
            $ip = $_SERVER["HTTP_X_FORWARDED_FOR"];
        } elseif (!empty($_SERVER["REMOTE_ADDR"])) {
            $ip = $_SERVER["REMOTE_ADDR"];
        } else {
            $ip = "127.0.0.1";
        }
        
        // 如果有多个IP，取第一个
        if (strpos($ip, ',') !== false) {
            $ips = explode(',', $ip);
            $ip = trim($ips[0]);
        }
        
        return $ip;
    }
    
    /**
     * 处理支付回调
     * @param callable $callback 回调处理函数
     */
    public function handleNotify($callback) {
        $xml = file_get_contents('php://input');
        if (empty($xml)) {
            echo 'FAIL';
            return;
        }
        
        $data = $this->xmlToArray($xml);
        if (empty($data)) {
            echo 'FAIL';
            return;
        }
        
        // 验证签名
        if (!$this->verifyNotifySign($data)) {
            echo 'FAIL';
            return;
        }
        
        // 调用回调函数处理业务逻辑
        try {
            $result = call_user_func($callback, $data);
            echo $result ? 'SUCCESS' : 'FAIL';
        } catch (Exception $e) {
            echo 'FAIL';
        }
    }
}
