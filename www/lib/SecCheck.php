<?php

//类定义开始
class SecCheck
{
    // 错误信息
    private $error = '';
    // ACCESS_TOKEN
    private $token = '';

    /**
     * 架构函数
     * @access public
     * @param array $config  上传参数
     */
    public function __construct($config=array()) {
        //获取配置
        $config = x5()->import('config/config.ini.php');
        $APPID = $config['wx_app_id'];
        $APPSECRET = $config['wx_app_secret'];

        //实例化主资源类
        $resource = new Resource();
        //redis
        $redis = $resource->getRedis('redis');

        $access_token = $redis->get($APPID);
        if(empty($access_token)){
            //access_token
            $access_token = $this->getAccessToken($APPID,$APPSECRET);

            //access_token保存到Redis
            $redis->set($APPID, $access_token);
            $redis->expire($APPID, 7200);
        }
        $this->token = $access_token;
    }

    /**
     * 检查一段文本是否含有违法违规内容
     * @param  [type]  $content    [description]
     * @return [type]              [description]
     */
    public function msgSecCheck($content)
    {
        //access_token
        $access_token = $this->token;

        //请求地址
        $msg_sec_check_url = "https://api.weixin.qq.com/wxa/msg_sec_check?access_token=$access_token";

        $data = json_encode(array('content' => $content), JSON_UNESCAPED_UNICODE);
        $json = $this->httpRequest($msg_sec_check_url, $data, 'POST');
        $res = json_decode($json, true);

        return array("code" => '200', "info" => $res);
    }

    /**
     * 校验一张图片是否含有违法违规内容
     * @param  [type]  $media      [FormData]
     * @return [type]              [description]
     */
    public function imgSecCheck($filePath)
    {
        //access_token
        $access_token = $this->token;

        //请求地址
        $msg_sec_check_url = "https://api.weixin.qq.com/wxa/img_sec_check?access_token=$access_token";

        //数据处理
        $file_data = array("media"  => new \CURLFile($filePath));

        $json = $this->httpRequest($msg_sec_check_url, $file_data, 'POST');
        $res = json_decode($json, true);

        return array("code" => '200', "info" => $res);
    }

    /**
     * 获取access_token
     * @param  [type] $APPID     [description]
     * @param  [type] $APPSECRET [description]
     * @return [type]            [description]
     */
    public function getAccessToken($APPID,$APPSECRET)
    {
        //获取access_token
        $access_token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$APPID&secret=$APPSECRET";

        $json = $this->httpRequest($access_token_url);
        $res = json_decode($json, true);
        if(isset($res['errcode'])){
            return false;
        }

        return $res['access_token'];
    }

    /**
     * 把请求发送到微信服务器
     * @param  [type] $url    [请求地址]
     * @param  string $data   [发送参数]
     * @param  string $method [请求方式]
     * @return [type]         [description]
     */
    public function httpRequest($url, $data = '', $method = 'GET', $headers = array())
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);

        if(!empty($headers)){
            curl_setopt ($curl, CURLOPT_HTTPHEADER, $headers);
        }

        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);

        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

    // 重置图片文件大小
    public function resize_image($filename, $tmpname, $xmax=750, $ymax=1334)
    {
        $ext = explode(".", $filename);
        $ext = $ext[count($ext)-1];

        if($ext == "jpg" || $ext == "jpeg")
            $im = imagecreatefromjpeg($tmpname);
        elseif($ext == "png")
            $im = imagecreatefrompng($tmpname);
        elseif($ext == "gif")
            $im = imagecreatefromgif($tmpname);

        $x = imagesx($im);
        $y = imagesy($im);

        if($x <= $xmax && $y <= $ymax)
            return $im;

        if($x >= $y) {
            $newx = $xmax;
            $newy = $newx * $y / $x;
        }
        else {
            $newy = $ymax;
            $newx = $x / $y * $newy;
        }

        $im2 = imagecreatetruecolor($newx, $newy);
        imagecopyresized($im2, $im, 0, 0, 0, 0, floor($newx), floor($newy), $x, $y);
        return $im2;
    }
}
