<?php

class ApiMysql
{
        //private
	private $index;
	private $res;
	private $item;
	private $conn = null;
	private $config = null;
    //public
    public  $sth = null;
	public  $sql;
    public  $affectRows;
	
	public function __construct($config){
		$this->config = $config;
        $this->_connect();
	}

	public function execute()
	{
		return $this->_query();
	}
        
    public function executeAll()
    {
		if(empty($this->sql))
        	throw new Exception("Error:sql is null", 1003);

		$this->affectRows = $this->conn->exec($this->sql);
		return $this; 
    }

	public function sql($sql, $item = array())
	{
		$this->sql = $sql;
		$this->item = $item;
		return $this;
	}

	public function _query()
	{
		if(empty($this->sql))
            throw new Exception("Error:sql is null", 1003);

			$this->sth = $this->conn->prepare($this->sql, array(PDO::ATTR_CURSOR => pdo::CURSOR_FWDONLY));
            $res        = $this->sth->execute($this->item);
            if('00000' === $this->sth->errorCode()){
                return $res;
            }else{
                $errorInfo  = $this->sth->errorInfo();
                throw new Exception("Query error:".$errorInfo[2].' Sql:'.$this->sql, 2000);
            }
            return $this;
	}

	private function _connect()
	{
		if($this->conn == null || !$this->_ping())
		{
			$dsn = sprintf('mysql:host=%s;port=%d;dbname=%s', $this->config['dbhost'], $this->config['dbport'], $this->config['dbname']);
			try
			{
				$this->conn = new PDO($dsn, $this->config['dbuser'], $this->config['dbpass'] ,array());
				$this->conn->query("SET character_set_connection=".$this->config['charset'].", character_set_results=".$this->config['charset'].", character_set_client=binary");

			}
			catch(PDOException $e)
			{
                throw new Exception("Connect db failed:".$this->config['dbhost'].".Error:".$e->getMessage(), 1008);
			}

		}

	}

	private function _ping()
	{
		$status = $this->conn->getAttribute(PDO::ATTR_SERVER_INFO);
		if($status == 'MySQL server has gone away') return false;
		return true;
	}

	public function getRow()
	{
		return $this->sth->fetch(PDO::FETCH_ASSOC);
	}

	public function getRows()
	{
		return $this->sth->fetchAll(PDO::FETCH_ASSOC);
	}

	public function getUpdates()
	{
		return $this->sth->rowCount();
	}

	public function getInsertId()
	{
		return $this->conn->lastInsertId();
	}

	public function begin()
	{
		$this->conn->beginTransaction();
	}

	public function commit()
	{
		$this->conn->commit();
	}

        public function rollBack()
	{
		$this->conn->rollBack();
	}

}
