<?php

//类定义开始
class MiniQrCode
{
    //记录保存文件过程中出现的错误信息
    public static $error; 

    /**
     * 获取小程序分享二维码
     * @param  [type]  $site       [description]
     * @param  [type]  $mini_param [description]
     * @param  integer $width      [description]
     * @return [type]              [description]
     */
    public function getQrCode($site,$mini_param,$width = 280)
    {
        //获取配置
        $config = $this->getShareConfig($site);
        $APPID = $config['APPID'];
        $APPSECRET = $config['APPSECRET'];
        $mini_path = $config['MINI_PATH'];
        //获取access_token
        $access_token = $this->getAccessToken($APPID,$APPSECRET);
        if(empty($access_token)){
            return array("code"=>'400',"info"=>"access_token获取失败");
        }
        //小程序二维码地址
        $qcode_url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=$access_token";
        
        //构建请求参数
        $Attr = array(
            "scene" => $mini_param,
            "page" => $mini_path, 
            "width" => $width
        );
        $param = json_encode($Attr);

        //POST请求微信小程序接口，返回二维码图像二进制流
        $result = $this->httpRequest($qcode_url, $param, "POST");
        $res = json_decode($result,true);
        if($res['errcode'] != 0){
            return array("code"=>$res['errcode'],"info"=>$res['errmsg']);
        }

        $path = APP_PATH.'/upload/qrcode/';

        // 检查保存目录
        if(!is_dir($path)) {
            // 尝试创建目录
            if(!mkdir($path,0777,true)){
                return array("code"=>'400',"info"=>'上传目录'.$path.'不存在');
            }
        }else {
            if(!is_writeable($path)) {
                return array("code"=>'400',"info"=>'上传目录'.$path.'不可写');
            }
        }
        //文件名称
        $filename = uniqid().'.jpg';
        $file_path = $path.'/'.$filename;

        //生成二维码
        file_put_contents($file_path, $result);
        //$base64_image ="data:image/jpeg;base64,".base64_encode( $result );
        $img_url = $this->getServerHost()."/upload/qrcode/".$filename;

        return array("code"=>'200',"info"=>$img_url);
    }

    /**
     * 分享配置
     * @return [type] [description]
     */
    public function getShareConfig($site)
    {
        switch ($site) {
            //写作
            case '1':
                $APPID = 'wx56e28a55bbad2a90';
                $APPSECRET = 'aa01260bd2ca17daceea2b7f42d26b0c';
                $MINI_PATH = 'pages/worksInfo/worksInfo';
                break;
            //声乐
            case '2':
                $APPID = 'wx852824444334ca84';
                $APPSECRET = 'fd842ead7588ff3fc0d25856624d5ee3';
                $MINI_PATH = 'pages/worksInfo/worksInfo';
                break;
            //绘画
            case '3':
                $APPID = 'wxcfb179838e053d75';
                $APPSECRET = 'adc4c25b90359b0ce834515f789a4405';
                $MINI_PATH = 'pages/worksInfo/worksInfo';
                break;
        }
        $data['APPID'] = $APPID;
        $data['APPSECRET'] = $APPSECRET;
        $data['MINI_PATH'] = $MINI_PATH;

        return $data;
    }

    /**
     * 获取access_token
     * @param  [type] $APPID     [description]
     * @param  [type] $APPSECRET [description]
     * @return [type]            [description]
     */
    public function getAccessToken($APPID,$APPSECRET)
    {
        //实例化主资源类
        $resource = new Resource();
        //redis
        $redis = $resource->getRedis('redis');

        //获取access_token
        $access_token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$APPID&secret=$APPSECRET";

        $access_token = $redis->get($APPID);
        if(empty($access_token)){
            $json = $this->httpRequest($access_token_url);
            $res = json_decode($json, true);
            if(empty($res)){
                return false;
            }
            $access_token = $res['access_token'];
            //2、access_token保存到Redis
            $redis->set($APPID, $access_token);
            $redis->expire($APPID, 7200);
        }

        return $access_token;
    }

    /**
     * 获取完整的HOST
     * @return [type] [description]
     */
    public function getServerHost(){
        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $back_url = $http_type.$_SERVER['HTTP_HOST'];
        return $back_url;
    }

    /**
     * 把请求发送到微信服务器换取二维码
     * @param  [type] $url    [请求地址]
     * @param  string $data   [发送参数]
     * @param  string $method [请求方式]
     * @return [type]         [description]
     */
    public function httpRequest($url, $data = '', $method = 'GET')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }
}
