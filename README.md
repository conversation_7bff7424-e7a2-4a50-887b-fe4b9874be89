wms api项目要切换到配置文件管理系统上面，实现机制就是在服务器上，`配置文件管理系统`自动去生成对应环境的主要配置文件。<br />
以后正式和测试环境要增加配置项，都需要在`配置文件管理系统`添加key，然后在`real.php`之类的配置文件添加映射。<br />
下面这些针对测试环境的配置文件以后就不用了，我会从git里面会删除掉。谁还有在用这几个配置文件里面的配置的告诉我一声。

* test.php
* test.redis.ini.php
* mysql.ini.php

完整的运行配置文件如下面列表：

		.
		├── apps.ini.php
		├── engine.ini.php
		├── errorapp.ini.php
		├── errorsys.ini.php
		├── mysql.ini.php
		├── real.php
		├── redis.ini.php
		├── scm_config.php
		└── selected.php
		
其中`real.php`，`scm_config.php`,`selected.php`还是需要在本地自己创建，并本地排除，__不要发布提交!__

## scm_config.php文件

api项目会在config目录下面新增一个`scm_config.php`的文件，这个文件里面存的实际的配置。测试环境和线上会自动生成，开发者本地可以按照下面的代码自己新建一个。这个文件不要发布上线，我加到`.gitignore`文件里面。

    <?php
    return $scm_config = array (
        'wms.api.xiaomi.com.api.charset' => 'utf8',
        'wms.api.xiaomi.com.api.dbhost' => '*************',
        'wms.api.xiaomi.com.api.dbname' => 'xm_wms2_tm',
        'wms.api.xiaomi.com.api.dbpass' => '0NxYNLsL_2ey529y',
        'wms.api.xiaomi.com.api.dbport' => 3108,
        'wms.api.xiaomi.com.api.dbuser' => 'wms2_tm_w',
        'wms.api.xiaomi.com.mysql-ini.order_master.charset' => 'utf8',
        'wms.api.xiaomi.com.mysql-ini.order_master.dbhost' => '************',
        'wms.api.xiaomi.com.mysql-ini.order_master.dbname' => 'xm_shop',
        'wms.api.xiaomi.com.mysql-ini.order_master.dbpass' => 'xiaomitest8',
        'wms.api.xiaomi.com.mysql-ini.order_master.dbport' => 3306,
        'wms.api.xiaomi.com.mysql-ini.order_master.dbuser' => 'xiaomitest8',
        'wms.api.xiaomi.com.mysql-ini.order_slave.charset' => 'utf8',
        'wms.api.xiaomi.com.mysql-ini.order_slave.dbhost' => '************',
        'wms.api.xiaomi.com.mysql-ini.order_slave.dbname' => 'xm_shop',
        'wms.api.xiaomi.com.mysql-ini.order_slave.dbpass' => 'xiaomitest8',
        'wms.api.xiaomi.com.mysql-ini.order_slave.dbport' => 3306,
        'wms.api.xiaomi.com.mysql-ini.order_slave.dbuser' => 'xiaomitest8',
        'wms.api.xiaomi.com.mysql-ini.wms_api_master.charset' => 'utf8',
        'wms.api.xiaomi.com.mysql-ini.wms_api_master.dbhost' => 'wms2.mysql01.b2c.srv',
        'wms.api.xiaomi.com.mysql-ini.wms_api_master.dbname' => 'xm_wms2_tm',
        'wms.api.xiaomi.com.mysql-ini.wms_api_master.dbpass' => 'kIuTOF0_zKNgkCpj',
        'wms.api.xiaomi.com.mysql-ini.wms_api_master.dbport' => 3108,
        'wms.api.xiaomi.com.mysql-ini.wms_api_master.dbuser' => 'wms2_user',
        'wms.api.xiaomi.com.redis.api.enable' => true,
        'wms.api.xiaomi.com.redis.api.host' => '*************',
        'wms.api.xiaomi.com.redis.api.port' => 6379,
        'wms.api.xiaomi.com.redis.scm_stock.enable' => true,
        'wms.api.xiaomi.com.redis.scm_stock.host' => 'wmsredis01.b2c.srv',
        'wms.api.xiaomi.com.redis.scm_stock.port' => 8379,
        'wms.api.xiaomi.com.wms_api_master.charset' => 'utf8',
        'wms.api.xiaomi.com.wms_api_master.dbhost' => '*************',
        'wms.api.xiaomi.com.wms_api_master.dbname' => 'xm_wms2_tm',
        'wms.api.xiaomi.com.wms_api_master.dbpass' => '0NxYNLsL_2ey529y',
        'wms.api.xiaomi.com.wms_api_master.dbport' => 3108,
        'wms.api.xiaomi.com.wms_api_master.dbuser' => 'wms2_tm_w',
    );
## selected.php文件
这是以前选择那个配置文件的选择文件，现在固定写成如下代码，不要再做任何更新

	<?php
	//这里线上还是线下都不要做修改
	return include APP_PATH.'/config/real.php';
## real.php文件
Db等得配置主文件，本地按照下面代码新建。不要向master等远程分支提交.不要向线上和测试环境发布.

    <?php
	//数据库连接等配置
	include APP_PATH.'/config/scm_config.php';
	return array(
	    'api' => array(
	        'dbhost'  => $scm_config['wms.api.xiaomi.com.api.dbhost'],
	        'dbname'  => $scm_config['wms.api.xiaomi.com.api.dbname'],
	        'dbuser'  => $scm_config['wms.api.xiaomi.com.api.dbuser'],
	        'dbpass'  => $scm_config['wms.api.xiaomi.com.api.dbpass'],
	        'dbport'  => $scm_config['wms.api.xiaomi.com.api.dbport'],
	        'charset' => $scm_config['wms.api.xiaomi.com.api.charset'],
	    ),
	    'wms_api_master'=>array(
	        'dbhost'  => $scm_config['wms.api.xiaomi.com.wms_api_master.dbhost'],
	        'dbname'  => $scm_config['wms.api.xiaomi.com.wms_api_master.dbname'],
	        'dbuser'  => $scm_config['wms.api.xiaomi.com.wms_api_master.dbuser'],
	        'dbpass'  => $scm_config['wms.api.xiaomi.com.wms_api_master.dbpass'],
	        'dbport'  => $scm_config['wms.api.xiaomi.com.wms_api_master.dbport'],
	        'charset' => $scm_config['wms.api.xiaomi.com.wms_api_master.charset'],
	    ),
	);
	?>
